package com.pgs.service;

import com.pgs.dto.exception.ManualTransactionRequest;
import com.pgs.dto.exception.MatchTransactionRequest;
import com.pgs.entity.*;
import com.pgs.enums.*;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExceptionHandlingServiceTest {

    @Mock
    private DepositRequestRepository depositRequestRepository;
    
    @Mock
    private WithdrawalRequestRepository withdrawalRequestRepository;
    
    @Mock
    private BankTransactionRepository bankTransactionRepository;
    
    @Mock
    private BankAccountRepository bankAccountRepository;
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private AuditService auditService;
    
    @Mock
    private DepositService depositService;
    
    @Mock
    private WithdrawalService withdrawalService;

    @InjectMocks
    private ExceptionHandlingService exceptionHandlingService;

    private BankTransaction transaction;
    private DepositRequest depositRequest;
    private WithdrawalRequest withdrawalRequest;
    private BankAccount bankAccount;
    private MatchTransactionRequest matchRequest;

    @BeforeEach
    void setUp() {
        // Setup bank account
        bankAccount = BankAccount.builder()
            .id(UUID.randomUUID())
            .nickname("Test Account")
            .accountNumber("**********")
            .bankName("Test Bank")
            .currentBalance(new BigDecimal("1000.00"))
            .dailyInUsed(BigDecimal.ZERO)
            .dailyOutUsed(BigDecimal.ZERO)
            .freezedDailyInUsed(new BigDecimal("100.00"))
            .freezedDailyOutUsed(new BigDecimal("50.00"))
            .totalIn(BigDecimal.ZERO)
            .totalOut(BigDecimal.ZERO)
            .build();

        // Setup transaction
        transaction = BankTransaction.builder()
            .id(UUID.randomUUID())
            .bankAccount(bankAccount)
            .amount(new BigDecimal("100.00"))
            .direction(TransactionDirection.IN)
            .status(TransactionStatus.PENDING)
            .build();

        // Setup deposit request
        depositRequest = DepositRequest.builder()
            .id(UUID.randomUUID())
            .merchantUserId("merchant-user-123")
            .amount(new BigDecimal("100.00"))
            .status(DepositStatus.PENDING)
            .assignedBankAccount(bankAccount)
            .build();

        // Setup withdrawal request
        withdrawalRequest = WithdrawalRequest.builder()
            .id(UUID.randomUUID())
            .merchantUserId("merchant-user-456")
            .amount(new BigDecimal("50.00"))
            .status(WithdrawalStatus.PENDING)
            .bankAccount(bankAccount)
            .build();

        // Setup match request
        matchRequest = new MatchTransactionRequest();
        matchRequest.setTransactionId(transaction.getId().toString());
        matchRequest.setRequestId(depositRequest.getId().toString());
    }

    @Test
    void testMatchDepositTransactionToRequest_Success() {
        // Arrange
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.of(depositRequest));

        // Act
        exceptionHandlingService.matchDepositTransactionToRequest(matchRequest);

        // Assert
        assertEquals(depositRequest.getId(), transaction.getMatchedRequestId());
        assertEquals(TransactionStatus.COMPLETED, transaction.getStatus());
        assertEquals(transaction, depositRequest.getMatchedTransaction());
        assertEquals(DepositStatus.MATCHED, depositRequest.getStatus());
        
        verify(bankTransactionRepository).save(transaction);
        verify(depositRequestRepository).save(depositRequest);
        verify(bankAccountRepository).save(bankAccount);
        verify(auditService).logUserAction(any(), eq("MANUAL_MATCH"), eq("TRANSACTION"), eq(transaction.getId()), any());
    }

    @Test
    void testMatchWithdrawalTransactionToRequest_Success() {
        // Arrange
        transaction.setDirection(TransactionDirection.OUT); // Make it outgoing for withdrawal
        matchRequest.setRequestId(withdrawalRequest.getId().toString());
        
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(withdrawalRequestRepository.findById(withdrawalRequest.getId())).thenReturn(Optional.of(withdrawalRequest));

        // Act
        exceptionHandlingService.matchWithdrawalTransactionToRequest(matchRequest);

        // Assert
        assertEquals(withdrawalRequest.getId(), transaction.getMatchedRequestId());
        assertEquals(TransactionStatus.COMPLETED, transaction.getStatus());
        assertEquals(transaction, withdrawalRequest.getTransaction());
        assertEquals(WithdrawalStatus.SUCCESS, withdrawalRequest.getStatus());
        
        verify(bankTransactionRepository).save(transaction);
        verify(withdrawalRequestRepository).save(withdrawalRequest);
        verify(bankAccountRepository).save(bankAccount);
        verify(auditService).logUserAction(any(), eq("MANUAL_MATCH"), eq("TRANSACTION"), eq(transaction.getId()), any());
    }

    @Test
    void testMatchDepositTransactionToRequest_TransactionNotFound() {
        // Arrange
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchDepositTransactionToRequest_DepositRequestNotFound() {
        // Arrange
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchDepositTransactionToRequest_TransactionAlreadyMatched() {
        // Arrange
        transaction.setMatchedRequestId(UUID.randomUUID());
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.of(depositRequest));

        // Act & Assert
        assertThrows(BadRequestException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchDepositTransactionToRequest_OutgoingTransactionNotAllowed() {
        // Arrange
        transaction.setDirection(TransactionDirection.OUT);
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.of(depositRequest));

        // Act & Assert
        assertThrows(BadRequestException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchWithdrawalTransactionToRequest_IncomingTransactionNotAllowed() {
        // Arrange
        matchRequest.setRequestId(withdrawalRequest.getId().toString());
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(withdrawalRequestRepository.findById(withdrawalRequest.getId())).thenReturn(Optional.of(withdrawalRequest));

        // Act & Assert
        assertThrows(BadRequestException.class, () ->
            exceptionHandlingService.matchWithdrawalTransactionToRequest(matchRequest));
    }

    @Test
    void testCreateManualTransaction_WithCounterpartyAndReference() {
        // Arrange
        ManualTransactionRequest request = new ManualTransactionRequest();
        request.setBankAccountId(bankAccount.getId().toString());
        request.setType("deposit");
        request.setAmount(new BigDecimal("500.00"));
        request.setTimestamp(LocalDateTime.now());
        request.setReference("TEST_REF_123");

        ManualTransactionRequest.Counterparty counterparty = new ManualTransactionRequest.Counterparty();
        counterparty.setAccountNumber("**********");
        counterparty.setBankName("External Bank");
        request.setCounterparty(counterparty);

        when(bankAccountRepository.findById(bankAccount.getId())).thenReturn(Optional.of(bankAccount));

        // Act
        exceptionHandlingService.createManualTransaction(request);

        // Assert
        verify(bankTransactionRepository).save(any(BankTransaction.class));
        verify(bankAccountRepository).save(bankAccount);
        verify(auditService).logUserAction(any(), eq("MANUAL_TRANSACTION_CREATED"), eq("TRANSACTION"), any(), any());

        // Verify balance updates for deposit
        assertEquals(new BigDecimal("1500.00"), bankAccount.getCurrentBalance());
        assertEquals(new BigDecimal("500.00"), bankAccount.getTotalIn());
    }

    @Test
    void testCreateManualTransaction_WithoutCounterpartyAndReference() {
        // Arrange
        ManualTransactionRequest request = new ManualTransactionRequest();
        request.setBankAccountId(bankAccount.getId().toString());
        request.setType("withdrawal");
        request.setAmount(new BigDecimal("200.00"));
        request.setTimestamp(LocalDateTime.now());
        // No counterparty and reference set (should be null)

        when(bankAccountRepository.findById(bankAccount.getId())).thenReturn(Optional.of(bankAccount));

        // Act
        exceptionHandlingService.createManualTransaction(request);

        // Assert
        verify(bankTransactionRepository).save(any(BankTransaction.class));
        verify(bankAccountRepository).save(bankAccount);
        verify(auditService).logUserAction(any(), eq("MANUAL_TRANSACTION_CREATED"), eq("TRANSACTION"), any(), any());

        // Verify balance updates for withdrawal
        assertEquals(new BigDecimal("800.00"), bankAccount.getCurrentBalance());
        assertEquals(new BigDecimal("200.00"), bankAccount.getTotalOut());
    }

    @Test
    void testCreateManualTransaction_WithEmptyReference() {
        // Arrange
        ManualTransactionRequest request = new ManualTransactionRequest();
        request.setBankAccountId(bankAccount.getId().toString());
        request.setType("deposit");
        request.setAmount(new BigDecimal("300.00"));
        request.setTimestamp(LocalDateTime.now());
        request.setReference("   "); // Empty/whitespace reference

        when(bankAccountRepository.findById(bankAccount.getId())).thenReturn(Optional.of(bankAccount));

        // Act
        exceptionHandlingService.createManualTransaction(request);

        // Assert
        verify(bankTransactionRepository).save(any(BankTransaction.class));
        verify(bankAccountRepository).save(bankAccount);
        verify(auditService).logUserAction(any(), eq("MANUAL_TRANSACTION_CREATED"), eq("TRANSACTION"), any(), any());
    }

    @Test
    void testCreateManualTransaction_BankAccountNotFound() {
        // Arrange
        ManualTransactionRequest request = new ManualTransactionRequest();
        request.setBankAccountId(UUID.randomUUID().toString());
        request.setType("deposit");
        request.setAmount(new BigDecimal("100.00"));
        request.setTimestamp(LocalDateTime.now());

        when(bankAccountRepository.findById(any(UUID.class))).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () ->
            exceptionHandlingService.createManualTransaction(request));

        verify(bankTransactionRepository, never()).save(any());
        verify(bankAccountRepository, never()).save(any());
    }
}
