-- Replace user_id foreign key with merchant_user_id string field in deposit_requests
ALTER TABLE deposit_requests 
DROP CONSTRAINT deposit_requests_user_id_fkey,
ADD COLUMN merchant_user_id VARCHAR(255) NOT NULL DEFAULT '';

-- Copy existing user_id values to merchant_user_id as strings (for existing data)
UPDATE deposit_requests SET merchant_user_id = user_id::text;

-- Drop the old user_id column
ALTER TABLE deposit_requests DROP COLUMN user_id;

-- Replace user_id foreign key with merchant_user_id string field in withdrawal_requests
ALTER TABLE withdrawal_requests 
DROP CONSTRAINT withdrawal_requests_user_id_fkey,
ADD COLUMN merchant_user_id VARCHAR(255) NOT NULL DEFAULT '';

-- Copy existing user_id values to merchant_user_id as strings (for existing data)
UPDATE withdrawal_requests SET merchant_user_id = user_id::text;

-- Drop the old user_id column
ALTER TABLE withdrawal_requests DROP COLUMN user_id;
