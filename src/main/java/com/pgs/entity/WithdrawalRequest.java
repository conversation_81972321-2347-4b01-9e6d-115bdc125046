package com.pgs.entity;

import com.pgs.enums.WithdrawalStatus;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "withdrawal_requests")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WithdrawalRequest extends BaseEntity {

    @Column(name = "merchant_user_id", nullable = false)
    private String merchantUserId;

    @Column(nullable = false, length = 10)
    @Builder.Default
    private String currency = "THB";

    @Column(name = "merchant_code", nullable = false, length = 50)
    private String merchantCode;

    @Column(name = "order_number", nullable = false, length = 100)
    private String orderNumber;

    @Column(name = "extend_param", columnDefinition = "jsonb")
    private String extendParam;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bank_account_id", nullable = false)
    private BankAccount bankAccount;

    @Column(nullable = false, precision = 18, scale = 2)
    private BigDecimal amount;

    @Column(name = "target_account_number", nullable = false, length = 64)
    private String targetAccountNumber;

    @Column(name = "target_bank_name", nullable = false, length = 100)
    private String targetBankName;

    @Column(name = "target_account_holder", length = 100)
    private String targetAccountHolder;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    @Builder.Default
    private WithdrawalStatus status = WithdrawalStatus.PENDING;

    @Column(name = "instruction_sent")
    @Builder.Default
    private Boolean instructionSent = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "txn_id")
    private BankTransaction transaction;

    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;

    @Column(name = "qr_code", columnDefinition = "TEXT")
    private String qrCode;

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean canBeProcessed() {
        return WithdrawalStatus.PENDING.equals(status) && !isExpired();
    }
}
