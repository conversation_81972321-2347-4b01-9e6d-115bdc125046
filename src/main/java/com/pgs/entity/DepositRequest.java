package com.pgs.entity;

import com.pgs.enums.DepositStatus;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "deposit_requests")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DepositRequest extends BaseEntity {

    @Column(name = "merchant_user_id", nullable = false)
    private String merchantUserId;

    @Column(nullable = false, length = 10)
    @Builder.Default
    private String currency = "THB";

    @Column(name = "merchant_code", nullable = false, length = 50)
    private String merchantCode;

    @Column(name = "order_number", nullable = false, length = 100)
    private String orderNumber;

    @Column(name = "extend_param", columnDefinition = "jsonb")
    private String extendParam;

    @Column(nullable = false, precision = 18, scale = 2)
    private BigDecimal amount;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    @Builder.Default
    private DepositStatus status = DepositStatus.PENDING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_bank_account_id")
    private BankAccount assignedBankAccount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "matched_txn_id")
    private BankTransaction matchedTransaction;

    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean canBeMatched() {
        return DepositStatus.PENDING.equals(status) && !isExpired();
    }
}
